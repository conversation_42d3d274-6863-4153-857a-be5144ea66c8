﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Entry">
      <UniqueIdentifier>{b2e13447-07ee-4738-973e-dff75512b687}</UniqueIdentifier>
    </Filter>
    <Filter Include="OS-ImGui">
      <UniqueIdentifier>{5e80ef5d-5f4b-49ee-b443-120bb14cf27a}</UniqueIdentifier>
    </Filter>
    <Filter Include="OS-ImGui\imgui">
      <UniqueIdentifier>{ab218404-3f90-49c1-936f-945331e62f77}</UniqueIdentifier>
    </Filter>
    <Filter Include="Utils">
      <UniqueIdentifier>{dc44f983-6ac1-4cd3-892d-2e8877ec0950}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheats">
      <UniqueIdentifier>{6d31e8d7-d8c1-4306-ab66-a79fab5a28b7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheats\View">
      <UniqueIdentifier>{3d76d523-ebe1-46aa-982f-99e167ac6724}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheats\Data">
      <UniqueIdentifier>{ecf8f725-5819-4b5b-9e16-3ada7ee8c84f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheats\Offsets">
      <UniqueIdentifier>{0af61bf5-c236-403c-a0f8-48488f87bfcf}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheats\MenuConfig">
      <UniqueIdentifier>{4ee44a79-e9da-483b-8c8c-46209c06c557}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheats\Radar">
      <UniqueIdentifier>{e372b7a9-eba4-42de-9802-b1368314d465}</UniqueIdentifier>
    </Filter>
    <Filter Include="Utils\ConfigSaver">
      <UniqueIdentifier>{aa25e002-e95d-4db7-a900-3cd9edf6338f}</UniqueIdentifier>
    </Filter>
    <Filter Include="libs">
      <UniqueIdentifier>{6aefeac5-beb2-4848-9ca9-8d9f9b06378a}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="OS-ImGui\imgui\imconfig.h">
      <Filter>OS-ImGui\imgui</Filter>
    </ClInclude>
    <ClInclude Include="OS-ImGui\imgui\imgui.h">
      <Filter>OS-ImGui\imgui</Filter>
    </ClInclude>
    <ClInclude Include="OS-ImGui\imgui\imgui_impl_dx11.h">
      <Filter>OS-ImGui\imgui</Filter>
    </ClInclude>
    <ClInclude Include="OS-ImGui\imgui\imgui_impl_win32.h">
      <Filter>OS-ImGui\imgui</Filter>
    </ClInclude>
    <ClInclude Include="OS-ImGui\imgui\imgui_internal.h">
      <Filter>OS-ImGui\imgui</Filter>
    </ClInclude>
    <ClInclude Include="OS-ImGui\imgui\imstb_rectpack.h">
      <Filter>OS-ImGui\imgui</Filter>
    </ClInclude>
    <ClInclude Include="OS-ImGui\imgui\imstb_textedit.h">
      <Filter>OS-ImGui\imgui</Filter>
    </ClInclude>
    <ClInclude Include="OS-ImGui\imgui\imstb_truetype.h">
      <Filter>OS-ImGui\imgui</Filter>
    </ClInclude>
    <ClInclude Include="OS-ImGui\OS-ImGui.h">
      <Filter>OS-ImGui</Filter>
    </ClInclude>
    <ClInclude Include="OS-ImGui\OS-ImGui_Base.h">
      <Filter>OS-ImGui</Filter>
    </ClInclude>
    <ClInclude Include="OS-ImGui\OS-ImGui_Exception.hpp">
      <Filter>OS-ImGui</Filter>
    </ClInclude>
    <ClInclude Include="OS-ImGui\OS-ImGui_External.h">
      <Filter>OS-ImGui</Filter>
    </ClInclude>
    <ClInclude Include="OS-ImGui\OS-ImGui_Struct.h">
      <Filter>OS-ImGui</Filter>
    </ClInclude>
    <ClInclude Include="Utils\ProcessManager.hpp">
      <Filter>Utils</Filter>
    </ClInclude>
    <ClInclude Include="View.hpp">
      <Filter>Cheats\View</Filter>
    </ClInclude>
    <ClInclude Include="Entity.h">
      <Filter>Cheats\Data</Filter>
    </ClInclude>
    <ClInclude Include="Game.h">
      <Filter>Cheats\Data</Filter>
    </ClInclude>
    <ClInclude Include="Cheats.h">
      <Filter>Cheats</Filter>
    </ClInclude>
    <ClInclude Include="Bone.h">
      <Filter>Cheats\Data</Filter>
    </ClInclude>
    <ClInclude Include="Render.hpp">
      <Filter>Cheats</Filter>
    </ClInclude>
    <ClInclude Include="Bunnyhop.hpp">
      <Filter>Cheats</Filter>
    </ClInclude>
    <ClInclude Include="Offsets.h">
      <Filter>Cheats\Offsets</Filter>
    </ClInclude>
    <ClInclude Include="MenuConfig.hpp">
      <Filter>Cheats\MenuConfig</Filter>
    </ClInclude>
    <ClInclude Include="Radar\Radar.h">
      <Filter>Cheats\Radar</Filter>
    </ClInclude>
    <ClInclude Include="TriggerBot.h">
      <Filter>Cheats</Filter>
    </ClInclude>
    <ClInclude Include="Utils\ConfigMenu.hpp">
      <Filter>Utils\ConfigSaver</Filter>
    </ClInclude>
    <ClInclude Include="Utils\ConfigSaver.hpp">
      <Filter>Utils\ConfigSaver</Filter>
    </ClInclude>
    <ClInclude Include="Aimbot.hpp">
      <Filter>Cheats</Filter>
    </ClInclude>
    <ClInclude Include="Utils\Format.hpp">
      <Filter>Utils</Filter>
    </ClInclude>
    <ClInclude Include="GlobalVars.h">
      <Filter>Cheats\Data</Filter>
    </ClInclude>
    <ClInclude Include="Globals.hpp">
      <Filter>Cheats</Filter>
    </ClInclude>
    <ClInclude Include="AntiFlashbang.hpp" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="leechcore.h">
      <Filter>libs</Filter>
    </ClInclude>
    <ClInclude Include="vmmdll.h">
      <Filter>libs</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="OS-ImGui\imgui\imgui.cpp">
      <Filter>OS-ImGui\imgui</Filter>
    </ClCompile>
    <ClCompile Include="OS-ImGui\imgui\imgui_demo.cpp">
      <Filter>OS-ImGui\imgui</Filter>
    </ClCompile>
    <ClCompile Include="OS-ImGui\imgui\imgui_draw.cpp">
      <Filter>OS-ImGui\imgui</Filter>
    </ClCompile>
    <ClCompile Include="OS-ImGui\imgui\imgui_impl_dx11.cpp">
      <Filter>OS-ImGui\imgui</Filter>
    </ClCompile>
    <ClCompile Include="OS-ImGui\imgui\imgui_impl_win32.cpp">
      <Filter>OS-ImGui\imgui</Filter>
    </ClCompile>
    <ClCompile Include="OS-ImGui\imgui\imgui_tables.cpp">
      <Filter>OS-ImGui\imgui</Filter>
    </ClCompile>
    <ClCompile Include="OS-ImGui\imgui\imgui_widgets.cpp">
      <Filter>OS-ImGui\imgui</Filter>
    </ClCompile>
    <ClCompile Include="OS-ImGui\OS-ImGui.cpp">
      <Filter>OS-ImGui</Filter>
    </ClCompile>
    <ClCompile Include="OS-ImGui\OS-ImGui_Base.cpp">
      <Filter>OS-ImGui</Filter>
    </ClCompile>
    <ClCompile Include="OS-ImGui\OS-ImGui_External.cpp">
      <Filter>OS-ImGui</Filter>
    </ClCompile>
    <ClCompile Include="main.cpp">
      <Filter>Entry</Filter>
    </ClCompile>
    <ClCompile Include="Game.cpp">
      <Filter>Cheats\Data</Filter>
    </ClCompile>
    <ClCompile Include="Entity.cpp">
      <Filter>Cheats\Data</Filter>
    </ClCompile>
    <ClCompile Include="Cheats.cpp">
      <Filter>Cheats</Filter>
    </ClCompile>
    <ClCompile Include="Bone.cpp">
      <Filter>Cheats\Data</Filter>
    </ClCompile>
    <ClCompile Include="Offsets.cpp">
      <Filter>Cheats\Offsets</Filter>
    </ClCompile>
    <ClCompile Include="Utils\MemorySearch.cpp">
      <Filter>Utils</Filter>
    </ClCompile>
    <ClCompile Include="Radar\Radar.cpp">
      <Filter>Cheats\Radar</Filter>
    </ClCompile>
    <ClCompile Include="TriggerBot.cpp">
      <Filter>Cheats</Filter>
    </ClCompile>
    <ClCompile Include="Utils\ConfigMenu.cpp">
      <Filter>Utils\ConfigSaver</Filter>
    </ClCompile>
    <ClCompile Include="Utils\ConfigSaver.cpp">
      <Filter>Utils\ConfigSaver</Filter>
    </ClCompile>
    <ClCompile Include="GlobalVars.cpp">
      <Filter>Cheats\Data</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <Library Include="leechcore.lib">
      <Filter>libs</Filter>
    </Library>
    <Library Include="vmm.lib">
      <Filter>libs</Filter>
    </Library>
    <Library Include="DMALibrary.lib">
      <Filter>libs</Filter>
    </Library>
  </ItemGroup>
  <ItemGroup>
    <None Include="info.db">
      <Filter>libs</Filter>
    </None>
  </ItemGroup>
</Project>