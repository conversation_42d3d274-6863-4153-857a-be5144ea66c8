﻿  Bone.cpp
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(10,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Bone.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(9,9):
      see previous definition of '_is_invalid'
  
  Cheats.cpp
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(10,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Cheats.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(9,9):
      see previous definition of '_is_invalid'
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(31,18): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(33,51): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(34,53): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(35,14): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(45,29): warning C4018: '>': signed/unsigned mismatch
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\Render.hpp(14,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\Render.hpp(37,46): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\Render.hpp(62,36): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\Render.hpp(79,57): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\Render.hpp(80,19): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\Render.hpp(82,70): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\Render.hpp(122,20): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\Render.hpp(124,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\Render.hpp(125,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\Render.hpp(126,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\Render.hpp(226,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\Render.hpp(258,34): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\Render.hpp(310,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\Render.hpp(342,34): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\Cheats.cpp(363,57): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Cheats.cpp(363,26): warning C4244: 'argument': conversion from 'DWORD64' to 'DWORD', possible loss of data
  Entity.cpp
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(10,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Entity.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(9,9):
      see previous definition of '_is_invalid'
  
  Game.cpp
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(10,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Game.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(9,9):
      see previous definition of '_is_invalid'
  
  GlobalVars.cpp
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(10,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(9,9):
      see previous definition of '_is_invalid'
  
  main.cpp
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(10,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'main.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(9,9):
      see previous definition of '_is_invalid'
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(31,18): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(33,51): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(34,53): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(35,14): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(45,29): warning C4018: '>': signed/unsigned mismatch
  (compiling source file 'main.cpp')
  
  Offsets.cpp
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(10,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Offsets.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(9,9):
      see previous definition of '_is_invalid'
  
R:\CS2_DMA_Extrnal-main\CS2_External\Offsets.cpp(33,35): warning C4244: '=': conversion from 'DWORD64' to 'DWORD', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Offsets.cpp(39,46): warning C4244: '=': conversion from 'DWORD64' to 'DWORD', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Offsets.cpp(45,31): warning C4244: '=': conversion from 'DWORD64' to 'DWORD', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Offsets.cpp(51,35): warning C4244: '=': conversion from 'DWORD64' to 'DWORD', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Offsets.cpp(59,43): warning C4244: '=': conversion from 'DWORD64' to 'DWORD', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Offsets.cpp(65,55): warning C4244: '=': conversion from 'DWORD64' to 'DWORD', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Offsets.cpp(71,41): warning C4244: '=': conversion from 'DWORD64' to 'DWORD', possible loss of data
  imgui.cpp
  imgui_demo.cpp
  imgui_draw.cpp
  imgui_impl_dx11.cpp
  imgui_impl_win32.cpp
  imgui_tables.cpp
  imgui_widgets.cpp
  OS-ImGui.cpp
  OS-ImGui_Base.cpp
  OS-ImGui_External.cpp
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(10,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(9,9):
      see previous definition of '_is_invalid'
  
  Radar.cpp
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(10,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(9,9):
      see previous definition of '_is_invalid'
  
R:\CS2_DMA_Extrnal-main\CS2_External\Radar\Radar.cpp(8,89): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Radar\Radar.cpp(9,89): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Radar\Radar.cpp(62,17): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Radar\Radar.cpp(64,62): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Radar\Radar.cpp(65,42): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Radar\Radar.cpp(134,28): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Radar\Radar.cpp(135,28): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Radar\Radar.cpp(163,63): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Radar\Radar.cpp(164,57): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Radar\Radar.cpp(166,34): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Radar\Radar.cpp(167,34): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Radar\Radar.cpp(169,36): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Radar\Radar.cpp(170,36): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Radar\Radar.cpp(172,36): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Radar\Radar.cpp(173,36): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  TriggerBot.cpp
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(10,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(9,9):
      see previous definition of '_is_invalid'
  
  ConfigMenu.cpp
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(10,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(9,9):
      see previous definition of '_is_invalid'
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(31,18): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(33,51): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(34,53): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(35,14): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(45,29): warning C4018: '>': signed/unsigned mismatch
  (compiling source file 'Utils/ConfigMenu.cpp')
  
  Compiling...
  ConfigSaver.cpp
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(10,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(9,9):
      see previous definition of '_is_invalid'
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(31,18): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(33,51): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(34,53): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(35,14): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2_DMA_Extrnal-main\CS2_External\AimBot.hpp(45,29): warning C4018: '>': signed/unsigned mismatch
  (compiling source file 'Utils/ConfigSaver.cpp')
  
  MemorySearch.cpp
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(120,15):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(471,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\leechcore.h(480,14):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(353,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1205,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1215,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1222,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1231,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1240,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1255,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1265,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1272,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1279,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1288,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1299,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1308,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1316,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1325,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1334,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\includes\vmmdll.h(1699,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(10,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Utils/MemorySearch.cpp')
      R:\CS2_DMA_Extrnal-main\CS2_External\Utils\ProcessManager.hpp(9,9):
      see previous definition of '_is_invalid'
  
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\MemorySearch.cpp(23,28): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\MemorySearch.cpp(38,22): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\MemorySearch.cpp(40,26): warning C4018: '<': signed/unsigned mismatch
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\MemorySearch.cpp(44,35): warning C4018: '<': signed/unsigned mismatch
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\MemorySearch.cpp(49,29): warning C4018: '>=': signed/unsigned mismatch
R:\CS2_DMA_Extrnal-main\CS2_External\Utils\MemorySearch.cpp(91,101): warning C4244: 'argument': conversion from 'SIZE_T' to 'DWORD', possible loss of data
  Generating code
  Previous IPDB not found, fall back to full compilation.
  All 3242 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  Finished generating code
  CS2_External.vcxproj -> R:\CS2_DMA_Extrnal-main\CS2_External\x64\Release\CS2_GG.exe
  'pwsh.exe' is not recognized as an internal or external command,
  operable program or batch file.
